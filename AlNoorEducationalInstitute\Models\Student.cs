using System;
using System.ComponentModel.DataAnnotations;

namespace AlNoorEducationalInstitute.Models
{
    /// <summary>
    /// نموذج بيانات الطالب
    /// Student data model
    /// </summary>
    public class Student
    {
        /// <summary>
        /// المعرف الفريد للطالب
        /// </summary>
        public int StudentId { get; set; }

        /// <summary>
        /// الرقم التعريفي للطالب (يمكن أن يكون مخصص)
        /// </summary>
        [Required(ErrorMessage = "الرقم التعريفي مطلوب")]
        [StringLength(20, ErrorMessage = "الرقم التعريفي يجب أن يكون أقل من 20 حرف")]
        public string StudentNumber { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [StringLength(50, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 50 حرف")]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [StringLength(50, ErrorMessage = "الاسم الأخير يجب أن يكون أقل من 50 حرف")]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل باللغة العربية
        /// </summary>
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
        public string FullNameArabic { get; set; } = string.Empty;

        /// <summary>
        /// الاسم الكامل باللغة الإنجليزية (اختياري)
        /// </summary>
        [StringLength(100, ErrorMessage = "الاسم الإنجليزي يجب أن يكون أقل من 100 حرف")]
        public string? FullNameEnglish { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        [Required(ErrorMessage = "تاريخ الميلاد مطلوب")]
        public DateTime DateOfBirth { get; set; }

        /// <summary>
        /// مكان الميلاد
        /// </summary>
        [StringLength(100, ErrorMessage = "مكان الميلاد يجب أن يكون أقل من 100 حرف")]
        public string? PlaceOfBirth { get; set; }

        /// <summary>
        /// الجنس (ذكر/أنثى)
        /// </summary>
        [Required(ErrorMessage = "الجنس مطلوب")]
        public Gender Gender { get; set; }

        /// <summary>
        /// الجنسية
        /// </summary>
        [StringLength(50, ErrorMessage = "الجنسية يجب أن تكون أقل من 50 حرف")]
        public string? Nationality { get; set; }

        /// <summary>
        /// اسم ولي الأمر
        /// </summary>
        [Required(ErrorMessage = "اسم ولي الأمر مطلوب")]
        [StringLength(100, ErrorMessage = "اسم ولي الأمر يجب أن يكون أقل من 100 حرف")]
        public string GuardianName { get; set; } = string.Empty;

        /// <summary>
        /// صلة القرابة مع ولي الأمر
        /// </summary>
        [StringLength(50, ErrorMessage = "صلة القرابة يجب أن تكون أقل من 50 حرف")]
        public string? GuardianRelationship { get; set; }

        /// <summary>
        /// مهنة ولي الأمر
        /// </summary>
        [StringLength(100, ErrorMessage = "مهنة ولي الأمر يجب أن تكون أقل من 100 حرف")]
        public string? GuardianOccupation { get; set; }

        /// <summary>
        /// رقم هاتف ولي الأمر الأساسي
        /// </summary>
        [Required(ErrorMessage = "رقم هاتف ولي الأمر مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        public string GuardianPhone { get; set; } = string.Empty;

        /// <summary>
        /// رقم هاتف ولي الأمر الثانوي (اختياري)
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف الثانوي يجب أن يكون أقل من 20 رقم")]
        public string? GuardianPhone2 { get; set; }

        /// <summary>
        /// البريد الإلكتروني لولي الأمر (اختياري)
        /// </summary>
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string? GuardianEmail { get; set; }

        /// <summary>
        /// العنوان السكني
        /// </summary>
        [Required(ErrorMessage = "العنوان السكني مطلوب")]
        [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// معرف الفصل الدراسي الحالي
        /// </summary>
        public int? CurrentClassId { get; set; }

        /// <summary>
        /// معرف الفصل الدراسي الحالي (للتوافق مع الكود الموجود)
        /// </summary>
        public int? ClassId => CurrentClassId;

        /// <summary>
        /// المستوى التعليمي
        /// </summary>
        public EducationLevel EducationLevel { get; set; }

        /// <summary>
        /// الدرجة/المستوى الدراسي
        /// </summary>
        public string Grade { get; set; } = string.Empty;

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// تاريخ التسجيل في المؤسسة
        /// </summary>
        public DateTime EnrollmentDate { get; set; }

        /// <summary>
        /// حالة الطالب (نشط، متخرج، منقطع، منقول)
        /// </summary>
        public StudentStatus Status { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم الذي أنشأ السجل
        /// </summary>
        public int CreatedByUserId { get; set; }

        /// <summary>
        /// معرف المستخدم الذي عدل السجل آخر مرة
        /// </summary>
        public int LastModifiedByUserId { get; set; }

        /// <summary>
        /// هل الطالب نشط
        /// </summary>
        public bool IsActive => Status == StudentStatus.Active;

        // Navigation Properties
        public virtual Class? CurrentClass { get; set; }
    }

    /// <summary>
    /// تعداد الجنس
    /// </summary>
    public enum Gender
    {
        Male = 1,   // ذكر
        Female = 2  // أنثى
    }

    /// <summary>
    /// تعداد حالة الطالب
    /// </summary>
    public enum StudentStatus
    {
        Active = 1,     // نشط
        Graduated = 2,  // متخرج
        Dropped = 3,    // منقطع
        Transferred = 4 // منقول
    }
}
