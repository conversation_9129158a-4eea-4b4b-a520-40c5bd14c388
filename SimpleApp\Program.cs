using System;
using System.Windows.Forms;
using System.Drawing;

namespace AlNoorEducationalInstitute
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "معهد النور التعليمي - Al Noor Educational Institute";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = SystemIcons.Application;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إنشاء شريط القوائم
            var menuStrip = new MenuStrip();
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);

            // قائمة الطلاب
            var studentsMenu = new ToolStripMenuItem("إدارة الطلاب");
            studentsMenu.Click += (s, e) => ShowMessage("سيتم تطوير نظام إدارة الطلاب قريباً");
            menuStrip.Items.Add(studentsMenu);

            // قائمة الفصول
            var classesMenu = new ToolStripMenuItem("إدارة الفصول");
            classesMenu.Click += (s, e) => ShowMessage("سيتم تطوير نظام إدارة الفصول قريباً");
            menuStrip.Items.Add(classesMenu);

            // قائمة الدرجات
            var gradesMenu = new ToolStripMenuItem("إدارة الدرجات");
            gradesMenu.Click += (s, e) => ShowMessage("سيتم تطوير نظام إدارة الدرجات قريباً");
            menuStrip.Items.Add(gradesMenu);

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.Click += (s, e) => ShowMessage("سيتم تطوير نظام التقارير قريباً");
            menuStrip.Items.Add(reportsMenu);

            // قائمة الإعدادات
            var settingsMenu = new ToolStripMenuItem("الإعدادات");
            settingsMenu.Click += (s, e) => ShowMessage("سيتم تطوير نظام الإعدادات قريباً");
            menuStrip.Items.Add(settingsMenu);

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("المساعدة");
            var aboutItem = new ToolStripMenuItem("حول البرنامج");
            aboutItem.Click += (s, e) => ShowAbout();
            helpMenu.DropDownItems.Add(aboutItem);
            menuStrip.Items.Add(helpMenu);

            // إنشاء شريط الحالة
            var statusStrip = new StatusStrip();
            var statusLabel = new ToolStripStatusLabel("مرحباً بك في معهد النور التعليمي");
            var timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            statusStrip.Items.Add(statusLabel);
            statusStrip.Items.Add(new ToolStripStatusLabel() { Spring = true });
            statusStrip.Items.Add(timeLabel);
            this.Controls.Add(statusStrip);

            // إنشاء اللوحة الرئيسية
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 248, 255)
            };
            this.Controls.Add(mainPanel);

            // إضافة عنوان ترحيبي
            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في معهد النور التعليمي\nAl Noor Educational Institute\n\nنظام إدارة المؤسسات التعليمية",
                Font = new Font("Arial", 20, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                AutoSize = false
            };
            mainPanel.Controls.Add(welcomeLabel);

            // إضافة أزرار سريعة
            var buttonPanel = new Panel
            {
                Height = 100,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(230, 240, 250)
            };
            mainPanel.Controls.Add(buttonPanel);

            var btnStudents = new Button
            {
                Text = "إدارة الطلاب",
                Size = new Size(120, 40),
                Location = new Point(50, 30),
                BackColor = Color.LightBlue
            };
            btnStudents.Click += (s, e) => ShowMessage("سيتم تطوير نظام إدارة الطلاب قريباً");
            buttonPanel.Controls.Add(btnStudents);

            var btnClasses = new Button
            {
                Text = "إدارة الفصول",
                Size = new Size(120, 40),
                Location = new Point(200, 30),
                BackColor = Color.LightGreen
            };
            btnClasses.Click += (s, e) => ShowMessage("سيتم تطوير نظام إدارة الفصول قريباً");
            buttonPanel.Controls.Add(btnClasses);

            var btnGrades = new Button
            {
                Text = "إدارة الدرجات",
                Size = new Size(120, 40),
                Location = new Point(350, 30),
                BackColor = Color.LightYellow
            };
            btnGrades.Click += (s, e) => ShowMessage("سيتم تطوير نظام إدارة الدرجات قريباً");
            buttonPanel.Controls.Add(btnGrades);

            var btnReports = new Button
            {
                Text = "التقارير",
                Size = new Size(120, 40),
                Location = new Point(500, 30),
                BackColor = Color.LightCoral
            };
            btnReports.Click += (s, e) => ShowMessage("سيتم تطوير نظام التقارير قريباً");
            buttonPanel.Controls.Add(btnReports);

            // تحديث الوقت كل ثانية
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000;
            timer.Tick += (s, e) => timeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            timer.Start();
        }

        private void ShowMessage(string message)
        {
            MessageBox.Show(message, "معهد النور التعليمي", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowAbout()
        {
            MessageBox.Show(
                "معهد النور التعليمي\nAl Noor Educational Institute\n\nنظام إدارة المؤسسات التعليمية\nالإصدار 1.0\n\nتم التطوير بواسطة فريق التطوير",
                "حول البرنامج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }
    }

    public static class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                var form = new MainForm();
                Application.Run(form);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل البرنامج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
