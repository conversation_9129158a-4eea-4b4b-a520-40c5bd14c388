using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Drawing;
using System.IO;
using System.Data.SQLite;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using AlNoorEducationalInstitute.Models.Institution;
using AlNoorEducationalInstitute.Data;

namespace AlNoorEducationalInstitute.Services
{
    /// <summary>
    /// خدمة إدارة ملف المؤسسة والشعار
    /// Institution profile and logo management service
    /// </summary>
    public class InstitutionService : IInstitutionService
    {
        private readonly DatabaseManager _databaseManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<InstitutionService> _logger;
        private readonly string _connectionString;

        public InstitutionService(
            DatabaseManager databaseManager,
            IConfiguration configuration,
            ILogger<InstitutionService> logger)
        {
            _databaseManager = databaseManager;
            _configuration = configuration;
            _logger = logger;
            _connectionString = _configuration.GetConnectionString("DefaultConnection")
                ?? throw new InvalidOperationException("Connection string not found");
        }

        #region إدارة ملف المؤسسة

        /// <summary>
        /// الحصول على ملف المؤسسة
        /// </summary>
        public async Task<InstitutionProfile> GetInstitutionProfileAsync()
        {
            try
            {
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "SELECT * FROM InstitutionProfile LIMIT 1";
                using var command = new SQLiteCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return MapReaderToProfile((SQLiteDataReader)reader);
                }

                // إنشاء ملف افتراضي إذا لم يكن موجوداً
                var defaultProfile = new InstitutionProfile();
                await CreateInstitutionProfileAsync(defaultProfile);
                return defaultProfile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على ملف المؤسسة");
                throw;
            }
        }

        /// <summary>
        /// تحديث ملف المؤسسة
        /// </summary>
        public async Task<bool> UpdateInstitutionProfileAsync(InstitutionProfile profile)
        {
            try
            {
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    UPDATE InstitutionProfile SET
                        InstitutionName = @InstitutionName,
                        InstitutionNameEn = @InstitutionNameEn,
                        Description = @Description,
                        DescriptionEn = @DescriptionEn,
                        Address = @Address,
                        AddressEn = @AddressEn,
                        City = @City,
                        Region = @Region,
                        PostalCode = @PostalCode,
                        Country = @Country,
                        PhoneNumber = @PhoneNumber,
                        FaxNumber = @FaxNumber,
                        Email = @Email,
                        Website = @Website,
                        LicenseNumber = @LicenseNumber,
                        LicenseIssueDate = @LicenseIssueDate,
                        LicenseExpiryDate = @LicenseExpiryDate,
                        LicenseIssuedBy = @LicenseIssuedBy,
                        CommercialRegistrationNumber = @CommercialRegistrationNumber,
                        TaxNumber = @TaxNumber,
                        PrimaryColor = @PrimaryColor,
                        SecondaryColor = @SecondaryColor,
                        TextColor = @TextColor,
                        ShowLogoInReports = @ShowLogoInReports,
                        ShowLogoInInvoices = @ShowLogoInInvoices,
                        ShowLogoInCertificates = @ShowLogoInCertificates,
                        ShowLogoInLetters = @ShowLogoInLetters,
                        ShowLogoInLoginScreen = @ShowLogoInLoginScreen,
                        ShowLogoInDashboard = @ShowLogoInDashboard,
                        Type = @Type,
                        EstablishedYear = @EstablishedYear,
                        StudentCapacity = @StudentCapacity,
                        LastModifiedDate = @LastModifiedDate,
                        LastModifiedByUserId = @LastModifiedByUserId,
                        LastModifiedByUserName = @LastModifiedByUserName
                    WHERE ProfileId = @ProfileId";

                using var command = new SQLiteCommand(sql, connection);
                AddProfileParameters(command, profile);
                command.Parameters.AddWithValue("@LastModifiedDate", DateTime.Now);

                var rowsAffected = await command.ExecuteNonQueryAsync();
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث ملف المؤسسة");
                return false;
            }
        }

        /// <summary>
        /// إنشاء ملف مؤسسة جديد
        /// </summary>
        public async Task<bool> CreateInstitutionProfileAsync(InstitutionProfile profile)
        {
            try
            {
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    INSERT INTO InstitutionProfile (
                        InstitutionName, InstitutionNameEn, Description, DescriptionEn,
                        Address, AddressEn, City, Region, PostalCode, Country,
                        PhoneNumber, FaxNumber, Email, Website,
                        LicenseNumber, LicenseIssueDate, LicenseExpiryDate, LicenseIssuedBy,
                        CommercialRegistrationNumber, TaxNumber,
                        PrimaryColor, SecondaryColor, TextColor,
                        ShowLogoInReports, ShowLogoInInvoices, ShowLogoInCertificates,
                        ShowLogoInLetters, ShowLogoInLoginScreen, ShowLogoInDashboard,
                        Type, EstablishedYear, StudentCapacity,
                        CreatedDate, LastModifiedDate, LastModifiedByUserId, LastModifiedByUserName
                    ) VALUES (
                        @InstitutionName, @InstitutionNameEn, @Description, @DescriptionEn,
                        @Address, @AddressEn, @City, @Region, @PostalCode, @Country,
                        @PhoneNumber, @FaxNumber, @Email, @Website,
                        @LicenseNumber, @LicenseIssueDate, @LicenseExpiryDate, @LicenseIssuedBy,
                        @CommercialRegistrationNumber, @TaxNumber,
                        @PrimaryColor, @SecondaryColor, @TextColor,
                        @ShowLogoInReports, @ShowLogoInInvoices, @ShowLogoInCertificates,
                        @ShowLogoInLetters, @ShowLogoInLoginScreen, @ShowLogoInDashboard,
                        @Type, @EstablishedYear, @StudentCapacity,
                        @CreatedDate, @LastModifiedDate, @LastModifiedByUserId, @LastModifiedByUserName
                    )";

                using var command = new SQLiteCommand(sql, connection);
                AddProfileParameters(command, profile);
                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                command.Parameters.AddWithValue("@LastModifiedDate", DateTime.Now);

                var rowsAffected = await command.ExecuteNonQueryAsync();
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء ملف المؤسسة");
                return false;
            }
        }

        #endregion

        #region إدارة الشعار

        /// <summary>
        /// رفع شعار من ملف
        /// </summary>
        public async Task<LogoUploadResult> UploadLogoAsync(string filePath, int uploadedByUserId)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new LogoUploadResult
                    {
                        Success = false,
                        Message = "الملف غير موجود",
                        ErrorCode = "FILE_NOT_FOUND"
                    };
                }

                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var fileName = Path.GetFileName(filePath);
                var fileExtension = Path.GetExtension(filePath).ToLower();
                var fileType = GetMimeTypeFromExtension(fileExtension);

                return await UploadLogoAsync(fileBytes, fileName, fileType, uploadedByUserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع الشعار من ملف: {FilePath}", filePath);
                return new LogoUploadResult
                {
                    Success = false,
                    Message = "خطأ في رفع الملف",
                    ErrorCode = "UPLOAD_ERROR"
                };
            }
        }

        /// <summary>
        /// رفع شعار من بيانات
        /// </summary>
        public async Task<LogoUploadResult> UploadLogoAsync(byte[] imageData, string fileName, string fileType, int uploadedByUserId)
        {
            try
            {
                // التحقق من صحة البيانات
                var validationErrors = await ValidateLogoDataAsync(imageData, fileName, fileType);
                if (validationErrors.Count > 0)
                {
                    return new LogoUploadResult
                    {
                        Success = false,
                        Message = "فشل في التحقق من صحة الملف",
                        ValidationErrors = validationErrors
                    };
                }

                // حفظ الشعار في قاعدة البيانات
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    UPDATE InstitutionProfile SET
                        LogoImage = @LogoImage,
                        LogoFileType = @LogoFileType,
                        LogoFileName = @LogoFileName,
                        LogoFileSize = @LogoFileSize,
                        LogoUploadDate = @LogoUploadDate,
                        LogoUploadedByUserId = @LogoUploadedByUserId,
                        LastModifiedDate = @LastModifiedDate,
                        LastModifiedByUserId = @LastModifiedByUserId
                    WHERE ProfileId = (SELECT ProfileId FROM InstitutionProfile LIMIT 1)";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@LogoImage", imageData);
                command.Parameters.AddWithValue("@LogoFileType", fileType);
                command.Parameters.AddWithValue("@LogoFileName", fileName);
                command.Parameters.AddWithValue("@LogoFileSize", imageData.Length);
                command.Parameters.AddWithValue("@LogoUploadDate", DateTime.Now);
                command.Parameters.AddWithValue("@LogoUploadedByUserId", uploadedByUserId);
                command.Parameters.AddWithValue("@LastModifiedDate", DateTime.Now);
                command.Parameters.AddWithValue("@LastModifiedByUserId", uploadedByUserId);

                var rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    _logger.LogInformation("تم رفع الشعار بنجاح: {FileName}", fileName);

                    return new LogoUploadResult
                    {
                        Success = true,
                        Message = "تم رفع الشعار بنجاح",
                        LogoInfo = new LogoInfo
                        {
                            HasLogo = true,
                            FileName = fileName,
                            FileType = fileType,
                            FileSize = imageData.Length,
                            FileSizeFormatted = FormatFileSize(imageData.Length),
                            UploadDate = DateTime.Now,
                            ImageData = imageData
                        }
                    };
                }

                return new LogoUploadResult
                {
                    Success = false,
                    Message = "فشل في حفظ الشعار",
                    ErrorCode = "SAVE_FAILED"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع الشعار");
                return new LogoUploadResult
                {
                    Success = false,
                    Message = "خطأ في رفع الشعار",
                    ErrorCode = "UPLOAD_ERROR"
                };
            }
        }

        /// <summary>
        /// حذف الشعار
        /// </summary>
        public async Task<bool> DeleteLogoAsync(int deletedByUserId)
        {
            try
            {
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    UPDATE InstitutionProfile SET
                        LogoImage = NULL,
                        LogoFileType = NULL,
                        LogoFileName = NULL,
                        LogoFileSize = 0,
                        LogoUploadDate = NULL,
                        LogoUploadedByUserId = NULL,
                        LastModifiedDate = @LastModifiedDate,
                        LastModifiedByUserId = @LastModifiedByUserId
                    WHERE ProfileId = (SELECT ProfileId FROM InstitutionProfile LIMIT 1)";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@LastModifiedDate", DateTime.Now);
                command.Parameters.AddWithValue("@LastModifiedByUserId", deletedByUserId);

                var rowsAffected = await command.ExecuteNonQueryAsync();

                if (rowsAffected > 0)
                {
                    _logger.LogInformation("تم حذف الشعار بواسطة المستخدم: {UserId}", deletedByUserId);
                }

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الشعار");
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات الشعار
        /// </summary>
        public async Task<LogoInfo?> GetLogoInfoAsync()
        {
            try
            {
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = @"
                    SELECT LogoImage, LogoFileType, LogoFileName, LogoFileSize,
                           LogoUploadDate, LogoUploadedByUserId
                    FROM InstitutionProfile
                    LIMIT 1";

                using var command = new SQLiteCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    if (reader.IsDBNull(0)) // LogoImage
                    {
                        return new LogoInfo { HasLogo = false };
                    }

                    return new LogoInfo
                    {
                        HasLogo = true,
                        FileName = reader.IsDBNull(1) ? "" : reader.GetString(1), // LogoFileName
                        FileType = reader.IsDBNull(2) ? "" : reader.GetString(2), // LogoFileType
                        FileSize = reader.IsDBNull(3) ? 0 : reader.GetInt64(3), // LogoFileSize
                        FileSizeFormatted = FormatFileSize(reader.IsDBNull(3) ? 0 : reader.GetInt64(3)), // LogoFileSize
                        UploadDate = reader.IsDBNull(4) ? null : reader.GetDateTime(4), // LogoUploadDate
                        ImageData = (byte[])reader[0] // LogoImage
                    };
                }

                return new LogoInfo { HasLogo = false };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات الشعار");
                return null;
            }
        }

        /// <summary>
        /// الحصول على صورة الشعار
        /// </summary>
        public async Task<byte[]?> GetLogoImageAsync()
        {
            try
            {
                using var connection = new SQLiteConnection(_connectionString);
                await connection.OpenAsync();

                var sql = "SELECT LogoImage FROM InstitutionProfile WHERE LogoImage IS NOT NULL LIMIT 1";
                using var command = new SQLiteCommand(sql, connection);

                var result = await command.ExecuteScalarAsync();
                return result as byte[];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على صورة الشعار");
                return null;
            }
        }

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة ملف الشعار
        /// </summary>
        public async Task<List<string>> ValidateLogoFileAsync(string filePath)
        {
            var errors = new List<string>();

            try
            {
                if (!File.Exists(filePath))
                {
                    errors.Add("الملف غير موجود");
                    return errors;
                }

                var fileInfo = new FileInfo(filePath);
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var fileName = Path.GetFileName(filePath);
                var fileExtension = Path.GetExtension(filePath).ToLower();

                return await ValidateLogoDataAsync(fileBytes, fileName, GetMimeTypeFromExtension(fileExtension));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من صحة ملف الشعار");
                errors.Add("خطأ في قراءة الملف");
                return errors;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات الشعار
        /// </summary>
        public async Task<List<string>> ValidateLogoDataAsync(byte[] imageData, string fileName, string fileType)
        {
            var errors = new List<string>();
            var settings = await GetLogoSettingsAsync();

            // التحقق من حجم الملف
            if (imageData.Length > settings.MaxFileSizeBytes)
            {
                errors.Add($"حجم الملف كبير جداً. الحد الأقصى المسموح: {FormatFileSize(settings.MaxFileSizeBytes)}");
            }

            if (imageData.Length == 0)
            {
                errors.Add("الملف فارغ");
            }

            // التحقق من نوع الملف
            if (!settings.AllowedFileTypes.Contains(fileType))
            {
                errors.Add($"نوع الملف غير مدعوم. الأنواع المدعومة: {string.Join(", ", settings.AllowedFileExtensions)}");
            }

            // التحقق من امتداد الملف
            var fileExtension = Path.GetExtension(fileName).ToLower();
            if (!settings.AllowedFileExtensions.Contains(fileExtension))
            {
                errors.Add($"امتداد الملف غير مدعوم. الامتدادات المدعومة: {string.Join(", ", settings.AllowedFileExtensions)}");
            }

            // التحقق من أن الملف صورة صحيحة
            try
            {
                using var ms = new MemoryStream(imageData);
                using var image = Image.FromStream(ms);

                if (image.Width < settings.MinWidthPixels || image.Height < settings.MinHeightPixels)
                {
                    errors.Add($"أبعاد الصورة صغيرة جداً. الحد الأدنى: {settings.MinWidthPixels}×{settings.MinHeightPixels}");
                }

                if (image.Width > settings.MaxWidthPixels || image.Height > settings.MaxHeightPixels)
                {
                    errors.Add($"أبعاد الصورة كبيرة جداً. الحد الأقصى: {settings.MaxWidthPixels}×{settings.MaxHeightPixels}");
                }
            }
            catch
            {
                errors.Add("الملف ليس صورة صحيحة");
            }

            return errors;
        }

        /// <summary>
        /// التحقق من صحة ملف الشعار (مبسط)
        /// </summary>
        public async Task<bool> IsValidLogoFileAsync(string filePath)
        {
            var errors = await ValidateLogoFileAsync(filePath);
            return errors.Count == 0;
        }

        /// <summary>
        /// التحقق من صحة بيانات الشعار (مبسط)
        /// </summary>
        public async Task<bool> IsValidLogoDataAsync(byte[] imageData, string fileType)
        {
            var errors = await ValidateLogoDataAsync(imageData, "temp.jpg", fileType);
            return errors.Count == 0;
        }

        #endregion

        #region إعدادات الشعار

        /// <summary>
        /// الحصول على إعدادات الشعار
        /// </summary>
        public async Task<LogoSettings> GetLogoSettingsAsync()
        {
            // إرجاع الإعدادات الافتراضية حالياً
            // يمكن تطويرها لاحقاً لحفظ الإعدادات في قاعدة البيانات
            return await GetDefaultLogoSettingsAsync();
        }

        /// <summary>
        /// الحصول على الإعدادات الافتراضية للشعار
        /// </summary>
        public async Task<LogoSettings> GetDefaultLogoSettingsAsync()
        {
            return await Task.FromResult(new LogoSettings());
        }

        #endregion

        #region معلومات الشعار

        /// <summary>
        /// التحقق من وجود شعار
        /// </summary>
        public async Task<bool> HasLogoAsync()
        {
            var logoInfo = await GetLogoInfoAsync();
            return logoInfo?.HasLogo ?? false;
        }

        /// <summary>
        /// الحصول على تاريخ رفع الشعار
        /// </summary>
        public async Task<DateTime?> GetLogoUploadDateAsync()
        {
            var logoInfo = await GetLogoInfoAsync();
            return logoInfo?.UploadDate;
        }

        /// <summary>
        /// الحصول على اسم ملف الشعار
        /// </summary>
        public async Task<string> GetLogoFileNameAsync()
        {
            var logoInfo = await GetLogoInfoAsync();
            return logoInfo?.FileName ?? "";
        }

        /// <summary>
        /// الحصول على نوع ملف الشعار
        /// </summary>
        public async Task<string> GetLogoFileTypeAsync()
        {
            var logoInfo = await GetLogoInfoAsync();
            return logoInfo?.FileType ?? "";
        }

        /// <summary>
        /// الحصول على حجم ملف الشعار
        /// </summary>
        public async Task<long> GetLogoFileSizeAsync()
        {
            var logoInfo = await GetLogoInfoAsync();
            return logoInfo?.FileSize ?? 0;
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// تحويل قارئ البيانات إلى كائن ملف المؤسسة
        /// </summary>
        private InstitutionProfile MapReaderToProfile(SQLiteDataReader reader)
        {
            return new InstitutionProfile
            {
                ProfileId = reader.GetInt32(0), // ProfileId
                InstitutionName = reader.GetString(1), // InstitutionName
                InstitutionNameEn = reader.IsDBNull(2) ? "" : reader.GetString(2), // InstitutionNameEn
                Description = reader.IsDBNull(3) ? "" : reader.GetString(3), // Description
                DescriptionEn = reader.IsDBNull(4) ? "" : reader.GetString(4), // DescriptionEn
                Address = reader.IsDBNull(5) ? "" : reader.GetString(5), // Address
                AddressEn = reader.IsDBNull(6) ? "" : reader.GetString(6), // AddressEn
                City = reader.IsDBNull(7) ? "" : reader.GetString(7), // City
                Region = reader.IsDBNull(8) ? "" : reader.GetString(8), // Region
                PostalCode = reader.IsDBNull(9) ? "" : reader.GetString(9), // PostalCode
                Country = reader.IsDBNull(10) ? "" : reader.GetString(10), // Country
                PhoneNumber = reader.IsDBNull(11) ? "" : reader.GetString(11), // PhoneNumber
                FaxNumber = reader.IsDBNull(12) ? "" : reader.GetString(12), // FaxNumber
                Email = reader.IsDBNull(13) ? "" : reader.GetString(13), // Email
                Website = reader.IsDBNull(14) ? "" : reader.GetString(14), // Website
                LicenseNumber = reader.IsDBNull(15) ? "" : reader.GetString(15), // LicenseNumber
                LicenseIssueDate = reader.IsDBNull(16) ? null : reader.GetDateTime(16), // LicenseIssueDate
                LicenseExpiryDate = reader.IsDBNull(17) ? null : reader.GetDateTime(17), // LicenseExpiryDate
                LicenseIssuedBy = reader.IsDBNull(18) ? "" : reader.GetString(18), // LicenseIssuedBy
                CommercialRegistrationNumber = reader.IsDBNull(19) ? "" : reader.GetString(19), // CommercialRegistrationNumber
                TaxNumber = reader.IsDBNull(20) ? "" : reader.GetString(20), // TaxNumber
                LogoImage = reader.IsDBNull(21) ? null : (byte[])reader[21], // LogoImage
                LogoFileType = reader.IsDBNull(22) ? "" : reader.GetString(22), // LogoFileType
                LogoFileName = reader.IsDBNull(23) ? "" : reader.GetString(23), // LogoFileName
                LogoFileSize = reader.IsDBNull(24) ? 0 : reader.GetInt64(24), // LogoFileSize
                LogoUploadDate = reader.IsDBNull(25) ? null : reader.GetDateTime(25), // LogoUploadDate
                LogoUploadedByUserId = reader.IsDBNull(26) ? null : reader.GetInt32(26), // LogoUploadedByUserId
                PrimaryColor = reader.IsDBNull(27) ? "#2E8B57" : reader.GetString(27), // PrimaryColor
                SecondaryColor = reader.IsDBNull(28) ? "#FFD700" : reader.GetString(28), // SecondaryColor
                TextColor = reader.IsDBNull(29) ? "#2F4F4F" : reader.GetString(29), // TextColor
                ShowLogoInReports = !reader.IsDBNull(30) && reader.GetBoolean(30), // ShowLogoInReports
                ShowLogoInInvoices = !reader.IsDBNull(31) && reader.GetBoolean(31), // ShowLogoInInvoices
                ShowLogoInCertificates = !reader.IsDBNull(32) && reader.GetBoolean(32), // ShowLogoInCertificates
                ShowLogoInLetters = !reader.IsDBNull(33) && reader.GetBoolean(33), // ShowLogoInLetters
                ShowLogoInLoginScreen = !reader.IsDBNull(34) && reader.GetBoolean(34), // ShowLogoInLoginScreen
                ShowLogoInDashboard = !reader.IsDBNull(35) && reader.GetBoolean(35), // ShowLogoInDashboard
                Type = reader.IsDBNull(36) ? InstitutionType.Private : (InstitutionType)reader.GetInt32(36), // Type
                EstablishedYear = reader.IsDBNull(37) ? 0 : reader.GetInt32(37), // EstablishedYear
                StudentCapacity = reader.IsDBNull(38) ? 0 : reader.GetInt32(38), // StudentCapacity
                CurrentStudentCount = reader.IsDBNull(39) ? 0 : reader.GetInt32(39), // CurrentStudentCount
                EmployeeCount = reader.IsDBNull(40) ? 0 : reader.GetInt32(40), // EmployeeCount
                CreatedDate = reader.GetDateTime(41), // CreatedDate
                LastModifiedDate = reader.GetDateTime(42), // LastModifiedDate
                LastModifiedByUserId = reader.GetInt32(43), // LastModifiedByUserId
                LastModifiedByUserName = reader.IsDBNull(44) ? "" : reader.GetString(44) // LastModifiedByUserName
            };
        }

        /// <summary>
        /// إضافة معاملات ملف المؤسسة للأمر
        /// </summary>
        private void AddProfileParameters(SQLiteCommand command, InstitutionProfile profile)
        {
            command.Parameters.AddWithValue("@ProfileId", profile.ProfileId);
            command.Parameters.AddWithValue("@InstitutionName", profile.InstitutionName);
            command.Parameters.AddWithValue("@InstitutionNameEn", profile.InstitutionNameEn);
            command.Parameters.AddWithValue("@Description", profile.Description ?? "");
            command.Parameters.AddWithValue("@DescriptionEn", profile.DescriptionEn ?? "");
            command.Parameters.AddWithValue("@Address", profile.Address ?? "");
            command.Parameters.AddWithValue("@AddressEn", profile.AddressEn ?? "");
            command.Parameters.AddWithValue("@City", profile.City ?? "");
            command.Parameters.AddWithValue("@Region", profile.Region ?? "");
            command.Parameters.AddWithValue("@PostalCode", profile.PostalCode ?? "");
            command.Parameters.AddWithValue("@Country", profile.Country ?? "");
            command.Parameters.AddWithValue("@PhoneNumber", profile.PhoneNumber ?? "");
            command.Parameters.AddWithValue("@FaxNumber", profile.FaxNumber ?? "");
            command.Parameters.AddWithValue("@Email", profile.Email ?? "");
            command.Parameters.AddWithValue("@Website", profile.Website ?? "");
            command.Parameters.AddWithValue("@LicenseNumber", profile.LicenseNumber ?? "");
            command.Parameters.AddWithValue("@LicenseIssueDate", profile.LicenseIssueDate);
            command.Parameters.AddWithValue("@LicenseExpiryDate", profile.LicenseExpiryDate);
            command.Parameters.AddWithValue("@LicenseIssuedBy", profile.LicenseIssuedBy ?? "");
            command.Parameters.AddWithValue("@CommercialRegistrationNumber", profile.CommercialRegistrationNumber ?? "");
            command.Parameters.AddWithValue("@TaxNumber", profile.TaxNumber ?? "");
            command.Parameters.AddWithValue("@PrimaryColor", profile.PrimaryColor);
            command.Parameters.AddWithValue("@SecondaryColor", profile.SecondaryColor);
            command.Parameters.AddWithValue("@TextColor", profile.TextColor);
            command.Parameters.AddWithValue("@ShowLogoInReports", profile.ShowLogoInReports);
            command.Parameters.AddWithValue("@ShowLogoInInvoices", profile.ShowLogoInInvoices);
            command.Parameters.AddWithValue("@ShowLogoInCertificates", profile.ShowLogoInCertificates);
            command.Parameters.AddWithValue("@ShowLogoInLetters", profile.ShowLogoInLetters);
            command.Parameters.AddWithValue("@ShowLogoInLoginScreen", profile.ShowLogoInLoginScreen);
            command.Parameters.AddWithValue("@ShowLogoInDashboard", profile.ShowLogoInDashboard);
            command.Parameters.AddWithValue("@Type", (int)profile.Type);
            command.Parameters.AddWithValue("@EstablishedYear", profile.EstablishedYear);
            command.Parameters.AddWithValue("@StudentCapacity", profile.StudentCapacity);
            command.Parameters.AddWithValue("@LastModifiedByUserId", profile.LastModifiedByUserId);
            command.Parameters.AddWithValue("@LastModifiedByUserName", profile.LastModifiedByUserName ?? "");
        }

        /// <summary>
        /// الحصول على نوع MIME من امتداد الملف
        /// </summary>
        private string GetMimeTypeFromExtension(string extension)
        {
            return extension.ToLower() switch
            {
                ".png" => "image/png",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// تنسيق حجم الملف
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        #endregion

        #region الدوال المطلوبة من الواجهة (تطبيق مبسط)

        public async Task<bool> ResetInstitutionProfileAsync() => throw new NotImplementedException();
        public async Task<byte[]?> GetLogoImageAsync(LogoSize size) => await GetLogoImageAsync();
        public async Task<Image?> GetLogoAsImageAsync() => throw new NotImplementedException();
        public async Task<Image?> GetLogoAsImageAsync(LogoSize size) => throw new NotImplementedException();
        public async Task<byte[]> ResizeLogoAsync(byte[] originalImage, int width, int height, bool maintainAspectRatio = true) => throw new NotImplementedException();
        public async Task<byte[]> ResizeLogoAsync(byte[] originalImage, LogoSize targetSize) => throw new NotImplementedException();
        public async Task<byte[]> CompressLogoAsync(byte[] originalImage, int quality = 85) => throw new NotImplementedException();
        public async Task<byte[]> ConvertLogoFormatAsync(byte[] originalImage, string targetFormat) => throw new NotImplementedException();
        public async Task<bool> UpdateLogoSettingsAsync(LogoSettings settings) => throw new NotImplementedException();
        public async Task<Size> GetLogoDimensionsAsync() => throw new NotImplementedException();
        public async Task<string> ExportLogoAsync(string exportPath) => throw new NotImplementedException();
        public async Task<bool> ImportLogoAsync(string importPath, int importedByUserId) => throw new NotImplementedException();
        public async Task<byte[]> GetLogoForExportAsync() => throw new NotImplementedException();
        public async Task<string> GetLogoBase64Async() => throw new NotImplementedException();
        public async Task<bool> ImportLogoFromBase64Async(string base64Data, string fileName, int importedByUserId) => throw new NotImplementedException();
        public async Task<bool> BackupLogoAsync(string backupPath) => throw new NotImplementedException();
        public async Task<bool> RestoreLogoAsync(string backupPath, int restoredByUserId) => throw new NotImplementedException();
        public async Task<bool> HasLogoBackupAsync(string backupPath) => throw new NotImplementedException();
        public async Task<object> GetLogoUsageStatisticsAsync() => throw new NotImplementedException();
        public async Task<List<string>> GetLogoUsageLocationsAsync() => throw new NotImplementedException();
        public async Task<object> GetLogoHistoryAsync() => throw new NotImplementedException();
        public async Task<bool> LogLogoOperationAsync(string operation, string details, int userId) => throw new NotImplementedException();
        public async Task<byte[]?> GetLogoForReportsAsync() => await GetLogoImageAsync();
        public async Task<byte[]?> GetLogoForInvoicesAsync() => await GetLogoImageAsync();
        public async Task<byte[]?> GetLogoForCertificatesAsync() => await GetLogoImageAsync();
        public async Task<byte[]?> GetLogoForLettersAsync() => await GetLogoImageAsync();
        public async Task<byte[]?> GetLogoForLoginScreenAsync() => await GetLogoImageAsync();
        public async Task<byte[]?> GetLogoForDashboardAsync() => await GetLogoImageAsync();
        public async Task<bool> IsLogoEnabledForReportsAsync() => true;
        public async Task<bool> IsLogoEnabledForInvoicesAsync() => true;
        public async Task<bool> IsLogoEnabledForCertificatesAsync() => true;
        public async Task<bool> IsLogoEnabledForLettersAsync() => true;
        public async Task<bool> IsLogoEnabledForLoginScreenAsync() => true;
        public async Task<bool> IsLogoEnabledForDashboardAsync() => true;
        public async Task<bool> SetLogoEnabledForReportsAsync(bool enabled) => throw new NotImplementedException();
        public async Task<bool> SetLogoEnabledForInvoicesAsync(bool enabled) => throw new NotImplementedException();
        public async Task<bool> SetLogoEnabledForCertificatesAsync(bool enabled) => throw new NotImplementedException();
        public async Task<bool> SetLogoEnabledForLettersAsync(bool enabled) => throw new NotImplementedException();
        public async Task<bool> SetLogoEnabledForLoginScreenAsync(bool enabled) => throw new NotImplementedException();
        public async Task<bool> SetLogoEnabledForDashboardAsync(bool enabled) => throw new NotImplementedException();
        public async Task<string> GetPrimaryColorAsync() => "#2E8B57";
        public async Task<string> GetSecondaryColorAsync() => "#FFD700";
        public async Task<string> GetTextColorAsync() => "#2F4F4F";
        public async Task<bool> UpdateColorsAsync(string primaryColor, string secondaryColor, string textColor) => throw new NotImplementedException();
        public async Task<Dictionary<string, string>> GetBrandColorsAsync() => throw new NotImplementedException();
        public async Task<string> GetInstitutionNameAsync() => "مؤسسة النور التربوي";
        public async Task<string> GetInstitutionNameEnAsync() => "Al-Noor Educational Institute";
        public async Task<string> GetAddressAsync() => "";
        public async Task<string> GetPhoneNumberAsync() => "";
        public async Task<string> GetEmailAsync() => "";
        public async Task<string> GetWebsiteAsync() => "";
        public async Task<string> GetLicenseNumberAsync() => "";
        public async Task<DateTime?> GetLicenseExpiryDateAsync() => null;
        public async Task<bool> IsLicenseValidAsync() => false;
        public async Task<int> GetDaysUntilLicenseExpiryAsync() => 0;
        public async Task<bool> UpdateLicenseInfoAsync(string licenseNumber, DateTime? issueDate, DateTime? expiryDate, string issuedBy) => throw new NotImplementedException();
        public async Task<List<string>> ValidateInstitutionProfileAsync(InstitutionProfile profile) => throw new NotImplementedException();
        public async Task<bool> IsInstitutionProfileCompleteAsync() => throw new NotImplementedException();
        public async Task<List<string>> GetMissingProfileFieldsAsync() => throw new NotImplementedException();
        public async Task<bool> CanUserManageLogoAsync(int userId) => true;
        public async Task<bool> CanUserViewLogoAsync(int userId) => true;
        public async Task<bool> CanUserManageProfileAsync(int userId) => true;
        public async Task<List<string>> GetUserPermissionsAsync(int userId) => throw new NotImplementedException();
        public async Task SendLogoUploadNotificationAsync(int userId, bool success, string message = "") => throw new NotImplementedException();
        public async Task SendLicenseExpiryNotificationAsync() => throw new NotImplementedException();
        public async Task<bool> ShouldNotifyLicenseExpiryAsync() => throw new NotImplementedException();
        public async Task<List<string>> GetNotificationRecipientsAsync() => throw new NotImplementedException();
        public async Task CleanupOldLogoVersionsAsync() => throw new NotImplementedException();
        public async Task OptimizeLogoStorageAsync() => throw new NotImplementedException();
        public async Task<object> GetStorageUsageAsync() => throw new NotImplementedException();
        public async Task<bool> RepairCorruptedLogoAsync() => throw new NotImplementedException();
        public async Task<List<object>> GetLogoAuditLogAsync(DateTime startDate, DateTime endDate) => throw new NotImplementedException();
        public async Task<List<object>> GetProfileAuditLogAsync(DateTime startDate, DateTime endDate) => throw new NotImplementedException();
        public async Task LogProfileChangeAsync(string fieldName, string oldValue, string newValue, int userId) => throw new NotImplementedException();
        public async Task<object> ExportProfileForAPIAsync() => throw new NotImplementedException();
        public async Task<object> ExportLogoForAPIAsync() => throw new NotImplementedException();
        public async Task<string> GetLogoUrlAsync() => throw new NotImplementedException();
        public async Task<bool> GenerateLogoThumbnailsAsync() => throw new NotImplementedException();
        public async Task<bool> EnableWatermarkAsync(bool enable) => throw new NotImplementedException();
        public async Task<bool> IsWatermarkEnabledAsync() => throw new NotImplementedException();
        public async Task<byte[]?> GetWatermarkedLogoAsync() => throw new NotImplementedException();
        public async Task<bool> SetLogoTransparencyAsync(float transparency) => throw new NotImplementedException();
        public async Task<float> GetLogoTransparencyAsync() => throw new NotImplementedException();
        public async Task<Image?> GetLogoForPrintingAsync(int dpi = 300) => throw new NotImplementedException();
        public async Task<byte[]?> GetLogoForPrintingAsync(int width, int height, int dpi = 300) => throw new NotImplementedException();
        public async Task<bool> OptimizeLogoForPrintingAsync() => throw new NotImplementedException();
        public async Task<int> GetLogoViewCountAsync() => throw new NotImplementedException();
        public async Task<int> GetLogoDownloadCountAsync() => throw new NotImplementedException();
        public async Task IncrementLogoViewCountAsync() => throw new NotImplementedException();
        public async Task IncrementLogoDownloadCountAsync() => throw new NotImplementedException();
        public async Task<object> GetLogoUsageAnalyticsAsync(DateTime startDate, DateTime endDate) => throw new NotImplementedException();
        public async Task<bool> AutoUpdateLogoInReportsAsync() => throw new NotImplementedException();
        public async Task<bool> AutoUpdateLogoInDocumentsAsync() => throw new NotImplementedException();
        public async Task<bool> RefreshLogoInAllApplicationsAsync() => throw new NotImplementedException();
        public async Task NotifyLogoUpdateToAllUsersAsync() => throw new NotImplementedException();

        #endregion
    }
}