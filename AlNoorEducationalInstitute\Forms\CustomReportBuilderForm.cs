using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using AlNoorEducationalInstitute.Services;
using AlNoorEducationalInstitute.Models.Reports;

namespace AlNoorEducationalInstitute.Forms
{
    /// <summary>
    /// نافذة منشئ التقارير المخصصة
    /// Custom report builder form
    /// </summary>
    public partial class CustomReportBuilderForm : Form
    {
        private readonly IAdvancedReportService _reportService;
        private readonly ILogger<CustomReportBuilderForm> _logger;
        private CustomReportBuilder _currentReport;

        // Main layout
        private TableLayoutPanel mainLayout;
        private Panel headerPanel;
        private Panel contentPanel;
        private Panel footerPanel;

        // Header controls
        private Label titleLabel;
        private TextBox reportNameTextBox;
        private TextBox descriptionTextBox;
        private ComboBox reportTypeComboBox;

        // Content tabs
        private TabControl contentTabs;
        private TabPage dataSourceTab;
        private TabPage fieldsTab;
        private TabPage filtersTab;
        private TabPage groupingTab;
        private TabPage previewTab;

        // Data source controls
        private ComboBox dataSourceComboBox;
        private Button refreshDataSourceButton;

        // Fields controls
        private CheckedListBox availableFieldsList;
        private ListBox selectedFieldsList;
        private Button addFieldButton;
        private Button removeFieldButton;
        private Button moveUpButton;
        private Button moveDownButton;

        // Filters controls
        private DataGridView filtersGrid;
        private Button addFilterButton;
        private Button removeFilterButton;

        // Grouping controls
        private CheckedListBox groupingFieldsList;
        private CheckBox showSubtotalsCheckBox;

        // Preview controls
        private DataGridView previewGrid;
        private Button previewButton;
        private Label previewStatusLabel;

        // Footer controls
        private Button saveButton;
        private Button saveAsTemplateButton;
        private Button executeButton;
        private Button cancelButton;

        public CustomReportBuilderForm(IAdvancedReportService reportService, ILogger<CustomReportBuilderForm> logger)
        {
            _reportService = reportService;
            _logger = logger;
            _currentReport = new CustomReportBuilder();

            InitializeComponent();
            SetupForm();
            LoadInitialData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "منشئ التقارير المخصصة";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            // Main layout
            mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                BackColor = Color.Transparent
            };

            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 120)); // Header
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Content
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60)); // Footer

            this.Controls.Add(mainLayout);
            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            SetupHeaderPanel();
            SetupContentPanel();
            SetupFooterPanel();

            // Add panels to main layout
            mainLayout.Controls.Add(headerPanel, 0, 0);
            mainLayout.Controls.Add(contentPanel, 0, 1);
            mainLayout.Controls.Add(footerPanel, 0, 2);

            // Event handlers
            dataSourceComboBox.SelectedIndexChanged += DataSourceComboBox_SelectedIndexChanged;
            addFieldButton.Click += AddFieldButton_Click;
            removeFieldButton.Click += RemoveFieldButton_Click;
            previewButton.Click += PreviewButton_Click;
            saveButton.Click += SaveButton_Click;
            executeButton.Click += ExecuteButton_Click;
            cancelButton.Click += (s, e) => this.Close();
        }

        private void SetupHeaderPanel()
        {
            headerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Title
            titleLabel = new Label
            {
                Text = "إنشاء تقرير مخصص جديد",
                Font = new Font("Tahoma", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 10),
                AutoSize = true
            };

            // Report name
            var reportNameLabel = new Label
            {
                Text = "اسم التقرير:",
                Location = new Point(20, 50),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            reportNameTextBox = new TextBox
            {
                Location = new Point(110, 50),
                Size = new Size(300, 25),
                Font = new Font("Tahoma", 10F)
            };

            // Description
            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(450, 50),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            descriptionTextBox = new TextBox
            {
                Location = new Point(520, 50),
                Size = new Size(300, 25),
                Font = new Font("Tahoma", 10F)
            };

            // Report type
            var reportTypeLabel = new Label
            {
                Text = "نوع التقرير:",
                Location = new Point(20, 85),
                Size = new Size(80, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            reportTypeComboBox = new ComboBox
            {
                Location = new Point(110, 85),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Tahoma", 10F)
            };

            // Populate report type combo
            reportTypeComboBox.Items.AddRange(new object[]
            {
                new ComboBoxItem("أكاديمي", ReportType.Academic),
                new ComboBoxItem("مالي", ReportType.Financial),
                new ComboBoxItem("إداري", ReportType.Administrative),
                new ComboBoxItem("إحصائي", ReportType.Statistical),
                new ComboBoxItem("تحليلي", ReportType.Analytical),
                new ComboBoxItem("مخصص", ReportType.Custom)
            });
            reportTypeComboBox.SelectedIndex = 0;

            headerPanel.Controls.AddRange(new Control[]
            {
                titleLabel, reportNameLabel, reportNameTextBox,
                descriptionLabel, descriptionTextBox,
                reportTypeLabel, reportTypeComboBox
            });
        }

        private void SetupContentPanel()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(20, 10, 20, 10)
            };

            // Content tabs
            contentTabs = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 10F, FontStyle.Regular)
            };

            // Data source tab
            dataSourceTab = new TabPage("مصدر البيانات");
            SetupDataSourceTab();

            // Fields tab
            fieldsTab = new TabPage("الحقول");
            SetupFieldsTab();

            // Filters tab
            filtersTab = new TabPage("المرشحات");
            SetupFiltersTab();

            // Grouping tab
            groupingTab = new TabPage("التجميع");
            SetupGroupingTab();

            // Preview tab
            previewTab = new TabPage("المعاينة");
            SetupPreviewTab();

            contentTabs.TabPages.AddRange(new TabPage[]
            {
                dataSourceTab, fieldsTab, filtersTab, groupingTab, previewTab
            });

            contentPanel.Controls.Add(contentTabs);
        }

        private void SetupDataSourceTab()
        {
            var dataSourceLabel = new Label
            {
                Text = "اختر مصدر البيانات:",
                Location = new Point(20, 20),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            dataSourceComboBox = new ComboBox
            {
                Location = new Point(180, 20),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Tahoma", 10F)
            };

            refreshDataSourceButton = new Button
            {
                Text = "تحديث",
                Location = new Point(490, 20),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var descriptionLabel = new Label
            {
                Text = "مصادر البيانات المتاحة تشمل جداول الطلاب، الموظفين، الفصول، المواد، الدرجات، الحضور، والبيانات المالية.",
                Location = new Point(20, 60),
                Size = new Size(600, 60),
                Font = new Font("Tahoma", 9F),
                ForeColor = Color.FromArgb(108, 117, 125)
            };

            dataSourceTab.Controls.AddRange(new Control[]
            {
                dataSourceLabel, dataSourceComboBox, refreshDataSourceButton, descriptionLabel
            });
        }

        private void SetupFieldsTab()
        {
            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                Padding = new Padding(10)
            };

            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 100));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40));
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Available fields
            var availableLabel = new Label
            {
                Text = "الحقول المتاحة:",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };

            availableFieldsList = new CheckedListBox
            {
                Dock = DockStyle.Fill,
                CheckOnClick = true,
                Font = new Font("Tahoma", 9F)
            };

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            addFieldButton = new Button
            {
                Text = "إضافة →",
                Size = new Size(80, 30),
                Location = new Point(10, 50),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            removeFieldButton = new Button
            {
                Text = "← إزالة",
                Size = new Size(80, 30),
                Location = new Point(10, 90),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            moveUpButton = new Button
            {
                Text = "↑ أعلى",
                Size = new Size(80, 30),
                Location = new Point(10, 130),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            moveDownButton = new Button
            {
                Text = "↓ أسفل",
                Size = new Size(80, 30),
                Location = new Point(10, 170),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonsPanel.Controls.AddRange(new Control[]
            {
                addFieldButton, removeFieldButton, moveUpButton, moveDownButton
            });

            // Selected fields
            var selectedLabel = new Label
            {
                Text = "الحقول المحددة:",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };

            selectedFieldsList = new ListBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 9F)
            };

            layout.Controls.Add(availableLabel, 0, 0);
            layout.Controls.Add(selectedLabel, 2, 0);
            layout.Controls.Add(availableFieldsList, 0, 1);
            layout.Controls.Add(buttonsPanel, 1, 1);
            layout.Controls.Add(selectedFieldsList, 2, 1);

            fieldsTab.Controls.Add(layout);
        }

        private void SetupFiltersTab()
        {
            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 2,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Height = 40
            };

            addFilterButton = new Button
            {
                Text = "إضافة مرشح",
                Size = new Size(100, 30),
                Location = new Point(10, 5),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            removeFilterButton = new Button
            {
                Text = "إزالة مرشح",
                Size = new Size(100, 30),
                Location = new Point(120, 5),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonsPanel.Controls.AddRange(new Control[] { addFilterButton, removeFilterButton });

            // Filters grid
            filtersGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = true,
                AllowUserToDeleteRows = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            // Setup filters grid columns
            filtersGrid.Columns.Add("Field", "الحقل");
            filtersGrid.Columns.Add("Operator", "المشغل");
            filtersGrid.Columns.Add("Value", "القيمة");
            filtersGrid.Columns.Add("LogicalOperator", "المشغل المنطقي");

            layout.Controls.Add(buttonsPanel, 0, 0);
            layout.Controls.Add(filtersGrid, 0, 1);

            filtersTab.Controls.Add(layout);
        }

        private void SetupGroupingTab()
        {
            var groupingLabel = new Label
            {
                Text = "اختر الحقول للتجميع:",
                Location = new Point(20, 20),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            groupingFieldsList = new CheckedListBox
            {
                Location = new Point(20, 50),
                Size = new Size(400, 200),
                CheckOnClick = true,
                Font = new Font("Tahoma", 9F)
            };

            showSubtotalsCheckBox = new CheckBox
            {
                Text = "إظهار المجاميع الفرعية",
                Location = new Point(20, 270),
                Size = new Size(200, 25),
                Checked = true
            };

            groupingTab.Controls.AddRange(new Control[]
            {
                groupingLabel, groupingFieldsList, showSubtotalsCheckBox
            });
        }

        private void SetupPreviewTab()
        {
            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));
            layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            layout.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));

            // Preview button
            previewButton = new Button
            {
                Text = "معاينة البيانات",
                Size = new Size(150, 30),
                Location = new Point(10, 5),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Dock = DockStyle.Left
            };

            var buttonPanel = new Panel { Dock = DockStyle.Fill };
            buttonPanel.Controls.Add(previewButton);

            // Preview grid
            previewGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false
            };

            // Status label
            previewStatusLabel = new Label
            {
                Text = "انقر على 'معاينة البيانات' لعرض نموذج من البيانات",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.FromArgb(108, 117, 125)
            };

            layout.Controls.Add(buttonPanel, 0, 0);
            layout.Controls.Add(previewGrid, 0, 1);
            layout.Controls.Add(previewStatusLabel, 0, 2);

            previewTab.Controls.Add(layout);
        }

        private void SetupFooterPanel()
        {
            footerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(20, 10, 20, 10)
            };

            saveButton = new Button
            {
                Text = "حفظ التقرير",
                Size = new Size(120, 35),
                Location = new Point(20, 12),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };

            saveAsTemplateButton = new Button
            {
                Text = "حفظ كقالب",
                Size = new Size(120, 35),
                Location = new Point(150, 12),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };

            executeButton = new Button
            {
                Text = "تنفيذ التقرير",
                Size = new Size(120, 35),
                Location = new Point(280, 12),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };

            cancelButton = new Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                Location = new Point(footerPanel.Width - 120, 12),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10F, FontStyle.Bold)
            };

            footerPanel.Controls.AddRange(new Control[]
            {
                saveButton, saveAsTemplateButton, executeButton, cancelButton
            });

            // Handle resize to reposition cancel button
            footerPanel.Resize += (s, e) =>
            {
                cancelButton.Location = new Point(footerPanel.Width - 120, 12);
            };
        }

        private async void LoadInitialData()
        {
            try
            {
                // Load data sources
                var dataSources = await _reportService.GetAvailableDataSourcesAsync();
                foreach (var source in dataSources)
                {
                    dataSourceComboBox.Items.Add(source);
                }

                if (dataSourceComboBox.Items.Count > 0)
                    dataSourceComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل البيانات الأولية");
                MessageBox.Show("خطأ في تحميل البيانات الأولية", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DataSourceComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (dataSourceComboBox.SelectedItem == null) return;

            try
            {
                var dataSource = dataSourceComboBox.SelectedItem.ToString();
                var fields = await _reportService.GetAvailableFieldsAsync(dataSource);

                availableFieldsList.Items.Clear();
                groupingFieldsList.Items.Clear();

                foreach (var field in fields)
                {
                    availableFieldsList.Items.Add(field.DisplayName);
                    groupingFieldsList.Items.Add(field.DisplayName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل حقول مصدر البيانات");
                MessageBox.Show("خطأ في تحميل حقول مصدر البيانات", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddFieldButton_Click(object sender, EventArgs e)
        {
            var checkedItems = availableFieldsList.CheckedItems.Cast<string>().ToList();
            foreach (var item in checkedItems)
            {
                if (!selectedFieldsList.Items.Contains(item))
                {
                    selectedFieldsList.Items.Add(item);
                }
            }
        }

        private void RemoveFieldButton_Click(object sender, EventArgs e)
        {
            if (selectedFieldsList.SelectedItem != null)
            {
                selectedFieldsList.Items.Remove(selectedFieldsList.SelectedItem);
            }
        }

        private async void PreviewButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateReportConfiguration())
                {
                    MessageBox.Show("يرجى التأكد من إعدادات التقرير", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                BuildCurrentReport();
                var previewData = await _reportService.PreviewReportDataAsync(_currentReport, 50);

                // Update preview grid (basic implementation)
                previewGrid.DataSource = previewData;
                previewStatusLabel.Text = "تم تحميل 50 سجل كمعاينة";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معاينة التقرير");
                MessageBox.Show("خطأ في معاينة التقرير", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateReportConfiguration())
                {
                    MessageBox.Show("يرجى التأكد من إعدادات التقرير", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                BuildCurrentReport();
                var reportId = await _reportService.SaveCustomReportAsync(_currentReport);

                if (reportId > 0)
                {
                    MessageBox.Show("تم حفظ التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التقرير", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ التقرير");
                MessageBox.Show("خطأ في حفظ التقرير", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ExecuteButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateReportConfiguration())
                {
                    MessageBox.Show("يرجى التأكد من إعدادات التقرير", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                BuildCurrentReport();
                var reportData = await _reportService.ExecuteCustomReportAsync(_currentReport);

                // Show report results (basic implementation)
                MessageBox.Show("تم تنفيذ التقرير بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ التقرير");
                MessageBox.Show("خطأ في تنفيذ التقرير", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateReportConfiguration()
        {
            if (string.IsNullOrWhiteSpace(reportNameTextBox.Text))
                return false;

            if (dataSourceComboBox.SelectedItem == null)
                return false;

            if (selectedFieldsList.Items.Count == 0)
                return false;

            return true;
        }

        private void BuildCurrentReport()
        {
            _currentReport.ReportName = reportNameTextBox.Text;
            _currentReport.Description = descriptionTextBox.Text;
            _currentReport.Type = (ReportType)((ComboBoxItem)reportTypeComboBox.SelectedItem).Value;
            _currentReport.DataSource = dataSourceComboBox.SelectedItem?.ToString() ?? "";
            _currentReport.CreatedDate = DateTime.Now;
            _currentReport.CreatedByUserId = 1; // Mock user ID

            // Build selected fields
            _currentReport.SelectedFields.Clear();
            for (int i = 0; i < selectedFieldsList.Items.Count; i++)
            {
                _currentReport.SelectedFields.Add(new ReportField
                {
                    FieldName = selectedFieldsList.Items[i].ToString(),
                    DisplayName = selectedFieldsList.Items[i].ToString(),
                    DataType = FieldType.Text,
                    IsVisible = true,
                    DisplayOrder = i
                });
            }

            // Build groupings
            _currentReport.Groupings.Clear();
            for (int i = 0; i < groupingFieldsList.CheckedItems.Count; i++)
            {
                _currentReport.Groupings.Add(new ReportGrouping
                {
                    FieldName = groupingFieldsList.CheckedItems[i].ToString(),
                    Level = i,
                    ShowSubtotals = showSubtotalsCheckBox.Checked
                });
            }
        }
    }
}