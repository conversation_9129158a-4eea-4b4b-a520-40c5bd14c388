using System;
using System.Windows.Forms;
using System.Drawing;

namespace AlNoorEducationalInstitute
{
    public partial class SimpleMainForm : Form
    {
        public SimpleMainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "معهد النور التعليمي - Al Noor Educational Institute";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = SystemIcons.Application;

            // إنشاء شريط القوائم
            var menuStrip = new MenuStrip();
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);

            // قائمة الطلاب
            var studentsMenu = new ToolStripMenuItem("إدارة الطلاب");
            studentsMenu.Click += (s, e) => MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            menuStrip.Items.Add(studentsMenu);

            // قائمة الفصول
            var classesMenu = new ToolStripMenuItem("إدارة الفصول");
            classesMenu.Click += (s, e) => MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            menuStrip.Items.Add(classesMenu);

            // قائمة الدرجات
            var gradesMenu = new ToolStripMenuItem("إدارة الدرجات");
            gradesMenu.Click += (s, e) => MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            menuStrip.Items.Add(gradesMenu);

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.Click += (s, e) => MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            menuStrip.Items.Add(reportsMenu);

            // إنشاء شريط الحالة
            var statusStrip = new StatusStrip();
            var statusLabel = new ToolStripStatusLabel("مرحباً بك في معهد النور التعليمي");
            var timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            statusStrip.Items.Add(statusLabel);
            statusStrip.Items.Add(new ToolStripStatusLabel() { Spring = true });
            statusStrip.Items.Add(timeLabel);
            this.Controls.Add(statusStrip);

            // إنشاء اللوحة الرئيسية
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.LightBlue
            };
            this.Controls.Add(mainPanel);

            // إضافة عنوان ترحيبي
            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في معهد النور التعليمي\nAl Noor Educational Institute",
                Font = new Font("Arial", 24, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            mainPanel.Controls.Add(welcomeLabel);

            // تحديث الوقت كل ثانية
            var timer = new System.Windows.Forms.Timer();
            timer.Interval = 1000;
            timer.Tick += (s, e) => timeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            timer.Start();
        }
    }

    public static class SimpleProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                var form = new SimpleMainForm();
                Application.Run(form);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل البرنامج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
